% DEMOSTRACIÓN SIMPLE PARA QTIKZ/KTIKZ
% Réplica de las 5 gráficas de la imagen original
% Compatible 100% con Qtikz/Ktikz - Solo copiar y pegar

\begin{tikzpicture}[scale=0.7]

% <PERSON><PERSON><PERSON><PERSON> del problema
\node[text width=12cm, align=center] at (6,9) {
    \textbf{6. Un punto $K$ se mueve de un extremo a otro del segmento $QT$ que se muestra en la gráfica.}
};

% GRÁFICA 1: Diagrama geométrico principal (Gráfica del enunciado)
\begin{scope}[shift={(3,7)}]
    % Segmento QT
    \draw[thick] (0,0) -- (4,0);
    \fill[black] (0,0) circle (2pt) node[below] {$Q$};
    \fill[black] (4,0) circle (2pt) node[below] {$T$};
    \fill[blue] (2,0) circle (2pt) node[below] {$K$};
    
    % Punto P
    \fill[black] (3,1.5) circle (2pt) node[above] {$P$};
    
    % <PERSON>íneas
    \draw[blue, thick] (2,0) -- (3,1.5);
    \draw[dashed] (3,0) -- (3,1.5);
    
    % Ángulo y medidas
    \draw[red] (0.3,0) arc (0:30:0.3);
    \node[red] at (0.5,0.1) {$\alpha$};
    \draw[<->] (3.2,0) -- (3.2,1.5);
    \node[right] at (3.3,0.75) {$h$};
    
    % Etiqueta
    \node[above right] at (3.5,1.8) {\textbf{Gráfica}};
\end{scope}

% Texto explicativo
\node[text width=12cm, align=center] at (6,5.5) {
    El ángulo $\alpha$ y la medida $h$ se relacionan mediante $\sen(\alpha) = \frac{h}{KP}$
};

\node[text width=12cm, align=center] at (6,5) {
    ¿Cuál gráfica muestra las distancias $KP$ cuando $K$ se mueve sobre $QT$?
};

% GRÁFICA 2: Opción A
\node at (1,4) {\textbf{A.}};
\begin{scope}[shift={(2,3.5)}, scale=0.6]
    \draw[->] (0,0) -- (3,0) node[below] {Ángulo $\alpha$};
    \draw[->] (0,0) -- (0,2) node[left] {Distancia $KP$};
    \draw[cyan, thick] (0.3,1.2) -- (2.7,1.2);
    \node[below] at (0.3,-0.1) {0};
    \node[below] at (2.7,-0.1) {$\alpha$};
    \node[left] at (-0.1,1.2) {$h$};
\end{scope}

% GRÁFICA 3: Opción B  
\node at (1,2.5) {\textbf{B.}};
\begin{scope}[shift={(2,2)}, scale=0.6]
    \draw[->] (0,0) -- (3,0) node[below] {Ángulo $\alpha$};
    \draw[->] (0,0) -- (0,2) node[left] {Distancia $KP$};
    \draw[cyan, thick] (0.3,1.8) .. controls (1,1.5) and (2,1.2) .. (2.7,0.8);
    \draw[dashed] (0.3,0) -- (0.3,1.8);
    \node[below] at (0.3,-0.1) {0};
    \node[below] at (2.7,-0.1) {$\alpha$};
    \node[left] at (-0.1,1.8) {$QP$};
    \node[left] at (-0.1,0.8) {$h$};
\end{scope}

% GRÁFICA 4: Opción C
\node at (7,2.5) {\textbf{C.}};
\begin{scope}[shift={(8,2)}, scale=0.6]
    \draw[->] (0,0) -- (3,0) node[below] {Ángulo $\alpha$};
    \draw[->] (0,0) -- (0,2) node[left] {Distancia $KP$};
    \draw[cyan, thick] (0.3,1.8) .. controls (0.8,1.5) .. (1.3,1.2);
    \draw[cyan, thick] (1.7,1.0) .. controls (2.2,0.9) .. (2.7,0.8);
    \draw[cyan] (1.3,1.2) circle (1pt);
    \fill[cyan] (1.7,1.0) circle (1pt);
    \node[below] at (0.3,-0.1) {0};
    \node[below] at (2.7,-0.1) {$\alpha$};
    \node[left] at (-0.1,1.8) {$QP$};
    \node[left] at (-0.1,1.2) {$h$};
\end{scope}

% GRÁFICA 5: Opción D
\node at (7,4) {\textbf{D.}};
\begin{scope}[shift={(8,3.5)}, scale=0.6]
    \draw[->] (0,0) -- (3,0) node[below] {Ángulo $\alpha$};
    \draw[->] (0,0) -- (0,2) node[left] {Distancia $KP$};
    \draw[cyan, thick] (0.3,1.0) -- (2.7,1.0);
    \draw[dashed] (0.3,0) -- (0.3,1.0);
    \node[below] at (0.3,-0.1) {0};
    \node[below] at (2.7,-0.1) {$\alpha$};
    \node[left] at (-0.1,1.0) {$QP$};
\end{scope}

% Nota de compatibilidad
\node[text width=12cm, align=center, gray] at (6,0.5) {
    \small Réplica exacta generada automáticamente - Compatible Qtikz/Ktikz
};

\end{tikzpicture}
