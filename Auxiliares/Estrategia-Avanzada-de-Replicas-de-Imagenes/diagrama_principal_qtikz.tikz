% Diagrama Geométrico Principal (Compatible Qtikz/Ktikz)
% Problema: Un punto K se mueve de un extremo a otro del segmento QT
\begin{tikzpicture}[scale=1.2]
% Segmento principal QT
\draw[thick] (0,0) -- (6,0);

% Puntos principales
\fill[black] (0,0) circle (3pt);
\fill[black] (6,0) circle (3pt);
\fill[blue] (3,0) circle (3pt);

% Etiquetas de puntos
\node[below] at (0,-0.3) {$Q$};
\node[below] at (6,-0.3) {$T$};
\node[below] at (3,-0.3) {$K$};

% Punto P (arriba del segmento)
\fill[black] (4,2) circle (3pt);
\node[above] at (4,2.2) {$P$};

% Líneas desde K hacia P
\draw[blue, thick] (3,0) -- (4,2);

% Líneas punteadas para mostrar la construcción geométrica
\draw[gray, dashed] (4,2) -- (4,0);
\draw[gray, dashed] (0,0) -- (4,2);

% Ángulo α
\draw[red] (0.5,0) arc (0:26.57:0.5);
\node[red] at (0.8,0.2) {$\alpha$};

% Distancia h (altura)
\draw[<->] (4.2,0) -- (4.2,2);
\node[right] at (4.3,1) {$h$};

% Distancia KP
\node[blue, above, sloped] at (3.5,1) {$KP$};

% Texto explicativo de la relación trigonométrica
\node[above] at (3,3) {$\sen(\alpha) = \frac{h}{KP}$};

% Indicación del movimiento de K
\draw[blue, <->] (1,0.5) -- (5,0.5);
\node[blue, above] at (3,0.7) {$K$ se mueve sobre $QT$};

% Marco del gráfico
\node[above right] at (5.5,2.5) {\textbf{Gráfica}};
\end{tikzpicture}
